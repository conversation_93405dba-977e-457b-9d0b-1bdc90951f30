<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Divider Fix Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-description {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.5;
        }
        
        /* Force scrollbar visibility for testing */
        .force-scrollbar .sales-scrollable-content {
            height: 200px;
            overflow-y: scroll;
        }
        
        .force-scrollbar .Sales-card-div {
            padding-right: 10px !important;
        }
        
        .force-scrollbar .sales-scrollable-content {
            padding-right: 8px !important;
        }
        
        /* Add extra content to force scrolling */
        .extra-content {
            height: 400px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            margin: 20px 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .toggle-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .toggle-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Sales Divider Fix Test</h1>
        <p>This test verifies that the sales divider spans the full width of the card container in both scrollbar states.</p>
        
        <div class="test-section">
            <div class="test-title">Test 1: Normal State (No Forced Scrollbar)</div>
            <div class="test-description">
                The divider should extend from the left edge to the right edge of the card.
            </div>
            
            <div class="sales-cards-container">
                <div class="sales-cards-row">
                    <!-- Today's Sales Card -->
                    <div class="Sales-card-div">
                        <div class="Sales-title-date-div">
                            <div class="title-date-section">
                                <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                                <div class="title-date-text">
                                    <span class="sales-card-title">Today's Sales</span>
                                    <span class="sales-card-date">Aug 4, 2025</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="sales-analytics-div">
                            <div class="sales-count-div">
                                <span class="sales-count">38</span>
                            </div>
                            <div class="analytics-div">
                                <div class="metric-col royalties-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value royalties">$426.46</span>
                                    </div>
                                    <span class="metric-label">Royalties</span>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col returned-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value returned">(-3)</span>
                                    </div>
                                    <div class="metric-label-row">
                                        <span class="metric-label">Returned</span>
                                        <span class="metric-percentage returned-percentage">7.9%</span>
                                    </div>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col cancelled-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value cancelled">0</span>
                                    </div>
                                    <span class="metric-label">Cancelled</span>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col new-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value new">0</span>
                                    </div>
                                    <span class="metric-label">New</span>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col ads-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value ads">15</span>
                                    </div>
                                    <div class="metric-label-row">
                                        <span class="metric-label">Ads</span>
                                        <span class="metric-percentage ads-percentage">39.5%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="sales-section-divider" />
                        
                        <div class="sales-scrollable-content">
                            <div class="marketplaces-div">
                                <div class="marketplaces-sales-row">
                                    <div class="marketplace-col all-marketplaces">
                                        <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
                                        <span class="marketplace-total-sales-count">38</span>
                                        <span class="marketplace-total-earned-royalties">$426.46</span>
                                        <span class="marketplace-total-returned-units">(-3)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Yesterday's Sales Card -->
                    <div class="Sales-card-div">
                        <div class="Sales-title-date-div">
                            <div class="title-date-section">
                                <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                                <div class="title-date-text">
                                    <span class="sales-card-title">Yesterday's Sales</span>
                                    <span class="sales-card-date">Aug 3, 2025</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="sales-analytics-div">
                            <div class="sales-count-div">
                                <span class="sales-count">42</span>
                            </div>
                            <div class="analytics-div">
                                <div class="metric-col royalties-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value royalties">$489.23</span>
                                    </div>
                                    <span class="metric-label">Royalties</span>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col returned-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value returned">(-2)</span>
                                    </div>
                                    <div class="metric-label-row">
                                        <span class="metric-label">Returned</span>
                                        <span class="metric-percentage returned-percentage">4.8%</span>
                                    </div>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col cancelled-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value cancelled">1</span>
                                    </div>
                                    <span class="metric-label">Cancelled</span>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col new-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value new">3</span>
                                    </div>
                                    <span class="metric-label">New</span>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-col ads-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value ads">18</span>
                                    </div>
                                    <div class="metric-label-row">
                                        <span class="metric-label">Ads</span>
                                        <span class="metric-percentage ads-percentage">42.9%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="sales-section-divider" />
                        
                        <div class="sales-scrollable-content">
                            <div class="marketplaces-div">
                                <div class="marketplaces-sales-row">
                                    <div class="marketplace-col all-marketplaces">
                                        <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
                                        <span class="marketplace-total-sales-count">42</span>
                                        <span class="marketplace-total-earned-royalties">$489.23</span>
                                        <span class="marketplace-total-returned-units">(-2)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 2: Forced Scrollbar State</div>
            <div class="test-description">
                The divider should still extend from the left edge to the right edge even when scrollbar is visible and card padding is reduced.
            </div>
            <button class="toggle-button" onclick="toggleScrollbar()">Toggle Scrollbar Test</button>
            
            <div class="sales-cards-container" id="scrollbar-test">
                <div class="sales-cards-row">
                    <!-- Today's Sales Card with forced scrollbar -->
                    <div class="Sales-card-div">
                        <div class="Sales-title-date-div">
                            <div class="title-date-section">
                                <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                                <div class="title-date-text">
                                    <span class="sales-card-title">Today's Sales (Scrollbar Test)</span>
                                    <span class="sales-card-date">Aug 4, 2025</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="sales-analytics-div">
                            <div class="sales-count-div">
                                <span class="sales-count">38</span>
                            </div>
                            <div class="analytics-div">
                                <div class="metric-col royalties-metric">
                                    <div class="metric-top">
                                        <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                                        <span class="metric-value royalties">$426.46</span>
                                    </div>
                                    <span class="metric-label">Royalties</span>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="sales-section-divider" />
                        
                        <div class="sales-scrollable-content">
                            <div class="marketplaces-div">
                                <div class="marketplaces-sales-row">
                                    <div class="marketplace-col all-marketplaces">
                                        <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
                                        <span class="marketplace-total-sales-count">38</span>
                                        <span class="marketplace-total-earned-royalties">$426.46</span>
                                        <span class="marketplace-total-returned-units">(-3)</span>
                                    </div>
                                </div>
                            </div>
                            <div class="extra-content">
                                Extra content to force scrolling
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleScrollbar() {
            const container = document.getElementById('scrollbar-test');
            container.classList.toggle('force-scrollbar');
            
            const button = document.querySelector('.toggle-button');
            if (container.classList.contains('force-scrollbar')) {
                button.textContent = 'Remove Scrollbar Test';
            } else {
                button.textContent = 'Toggle Scrollbar Test';
            }
        }
    </script>
</body>
</html>
